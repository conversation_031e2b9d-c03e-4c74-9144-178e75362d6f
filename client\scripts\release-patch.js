/*
 * Release patch script: bump version, commit, tag and push.
 * Cross-platform Node script to avoid reliance on npm lifecycle hooks.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function run(cmd, opts = {}) {
  const options = { stdio: 'inherit', ...opts };
  execSync(cmd, options);
}

function runCapture(cmd) {
  return execSync(cmd, { encoding: 'utf8' }).trim();
}

function tagExists(tag) {
  try {
    runCapture(`git rev-parse -q --verify refs/tags/${tag}`);
    return true;
  } catch {
    return false;
  }
}

function isAnnotatedTag(tag) {
  try {
    const type = runCapture(`git cat-file -t ${tag}`);
    return type === 'tag';
  } catch {
    return false;
  }
}


function ensureGitAvailable() {
  try {
    runCapture('git --version');
  } catch (e) {
    console.error('[release] Git not found. Please install Git and ensure it is on PATH.');
    process.exit(1);
  }
}

function ensureInsideGitRepo() {
  try {
    const inside = runCapture('git rev-parse --is-inside-work-tree');
    if (inside !== 'true') throw new Error('Not inside a git work tree');
  } catch (e) {
    console.error('[release] Not inside a Git repository. Please run this in your project with .git.');
    process.exit(1);
  }
}

function ensureCleanWorkingTree() {
  try {
    const status = runCapture('git status --porcelain');
    if (status && status.trim().length > 0) {
      console.warn('[release] Working tree not clean. Only version bump files will be committed.');
    }
  } catch (e) {
    // Ignore
  }
}

function main() {
  ensureGitAvailable();
  ensureInsideGitRepo();
  ensureCleanWorkingTree();

  const projectRoot = process.cwd();
  const pkgPath = path.join(projectRoot, 'package.json');
  const lockPath = path.join(projectRoot, 'package-lock.json');

  if (!fs.existsSync(pkgPath)) {
    console.error('[release] package.json not found in current directory.');
    process.exit(1);
  }

  console.log('[release] Bumping patch version (without auto git tag/commit)...');
  // Do not let npm version perform git actions; we handle them explicitly.
  run('npm version patch --git-tag-version=false');

  const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
  const version = pkg.version;
  const tag = `v${version}`;

  console.log(`[release] New version: ${version}`);

  console.log('[release] Staging version files...');
  run('git add package.json');
  if (fs.existsSync(lockPath)) {
    try { run('git add package-lock.json'); } catch {}
  }

  console.log('[release] Committing version bump...');
  run(`git commit -m "chore(release): ${tag}"`);

  console.log('[release] Ensuring tag exists...');
  if (!tagExists(tag)) {
    run(`git tag -a ${tag} -m "chore(release): ${tag}"`);
  } else {
    const annotated = isAnnotatedTag(tag);
    console.log(`[release] Tag ${tag} already exists locally (${annotated ? 'annotated' : 'lightweight'}).`);
  }

  console.log('[release] Pushing commit...');
  try {
    run('git push');
  } catch (e) {
    console.error('[release] git push failed. If this is a new branch, set upstream:');
    try {
      const branch = runCapture('git rev-parse --abbrev-ref HEAD');
      console.error(`  git push -u origin ${branch}`);
    } catch {}
    process.exit(1);
  }

  console.log('[release] Pushing tag...');
  try {
    // Push only the new tag explicitly to ensure it appears on remote even if no commits were pushed.
    run(`git push origin ${tag}`);
  } catch (e) {
    console.error('[release] Pushing tag failed. You may try: git push --tags');
    process.exit(1);
  }

  console.log('[release] Done.');
}

main();

