const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { requireApiKey } = require('../middleware/auth');

// 目标目录：server/uploads（静态服务路径 /uploads 对应此目录）
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// 确保目录存在
fs.mkdirSync(UPLOAD_DIR, { recursive: true });

// 使用原始文件名存储，且放宽类型限制，适用于 Electron 构建产物（yml、exe、msi、dmg、zip、AppImage、deb 等）
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    // 保留原始文件名（latest.yml / latest-mac.yml / latest-linux.yml 等需要固定文件名）
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 1024 * 1024 * 1024 // 1GB 上限，按需调整
  }
});

// 单文件上传（字段名 file）
router.post('/upload', requireApiKey, upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ status: 'error', message: '缺少文件(file)' });
  }

  // 返回可访问的 URL
  const fileUrl = `/uploads/${encodeURIComponent(req.file.originalname)}`;
  return res.json({
    status: 'success',
    message: '文件上传成功',
    data: {
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      mimetype: req.file.mimetype,
      path: req.file.path,
      url: fileUrl
    }
  });
});

module.exports = router;

