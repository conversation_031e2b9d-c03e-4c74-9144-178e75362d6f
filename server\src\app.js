/**
 * 主应用文件
 * 配置和启动 Express 应用
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 导入配置和工具
const { getConfig, validateConfig, printConfig } = require('./config/app');
const { connectDatabase, setupGracefulShutdown, healthCheck } = require('./config/database');
const { serverError, notFound } = require('./utils/response');

// 导入中间件
const { sanitizeInput } = require('./middleware/validation');
const { optionalAuth } = require('./middleware/auth');

// 导入路由
const brandRoutes = require('./routes/brands');
const userRoutes = require('./routes/users');

/**
 * 创建 Express 应用
 */
function createApp() {
  const app = express();
  const config = getConfig();

  // 基础中间件
  app.use(cors(config.cors));
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // 数据清理中间件
  app.use(sanitizeInput);

  // 可选认证中间件（为所有路由添加用户信息，如果有的话）
  app.use(optionalAuth);

  // 静态文件服务
  app.use('/public', express.static(path.join(__dirname, '../public')));
  app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

  // API 路由
  app.use('/api/brands', brandRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/files', require('./routes/files'));
  app.use('/api/releases', require('./routes/releases'));

  // 根路由
  app.get('/', (req, res) => {
    const serverInfo = require('./config/app').getServerInfo();
    res.json({
      name: serverInfo.name,
      version: serverInfo.version,
      description: serverInfo.description,
      environment: serverInfo.environment,
      status: 'running',
      timestamp: new Date().toISOString(),
      documentation: serverInfo.documentation
    });
  });

  // 健康检查路由
  app.get('/health', async (req, res) => {
    try {
      const dbHealth = await healthCheck();
      const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: dbHealth,
        memory: process.memoryUsage(),
        version: process.version
      };

      const statusCode = dbHealth.healthy ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error) {
      res.status(503).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message
      });
    }
  });

  // API 文档路由
  app.get('/api-docs', (req, res) => {
    try {
      const docPath = path.join(__dirname, '../API_DOCUMENTATION.md');
      
      if (fs.existsSync(docPath)) {
        const documentation = fs.readFileSync(docPath, 'utf8');
        res.set('Content-Type', 'text/markdown; charset=utf-8');
        res.send(documentation);
      } else {
        res.status(404).json({
          error: 'API 文档未找到',
          message: '文档文件不存在'
        });
      }
    } catch (error) {
      console.error('加载 API 文档失败:', error);
      res.status(500).json({
        error: '无法加载 API 文档',
        message: error.message
      });
    }
  });

  // API 信息路由
  app.get('/api', (req, res) => {
    res.json({
      message: 'AI重器 API 服务',
      version: 'v1',
      endpoints: {
        brands: '/api/brands',
        users: '/api/users',
        health: '/health',
        docs: '/api-docs'
      },
      timestamp: new Date().toISOString()
    });
  });

  // 404 处理
  app.use((req, res) => {
    notFound(res, `路由未找到: ${req.method} ${req.path}`);
  });

  // 全局错误处理中间件
  app.use((err, req, res, next) => {
    console.error('全局错误处理:', err);

    // 处理不同类型的错误
    if (err.name === 'ValidationError') {
      return res.status(400).json({
        status: 'error',
        message: '数据验证失败',
        errors: err.errors,
        timestamp: new Date().toISOString()
      });
    }

    if (err.name === 'CastError') {
      return res.status(400).json({
        status: 'error',
        message: '无效的数据格式',
        details: err.message,
        timestamp: new Date().toISOString()
      });
    }

    if (err.code === 11000) {
      return res.status(409).json({
        status: 'error',
        message: '数据已存在',
        details: '违反唯一性约束',
        timestamp: new Date().toISOString()
      });
    }

    // 自定义错误
    if (err.statusCode) {
      return res.status(err.statusCode).json({
        status: 'error',
        message: err.message,
        code: err.code,
        details: err.details,
        timestamp: new Date().toISOString()
      });
    }

    // 默认服务器错误
    serverError(res, '服务器内部错误', {
      message: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  });

  return app;
}

/**
 * 启动应用
 */
async function startApp() {
  try {
    // 验证配置
    validateConfig();

    // 连接数据库
    try {
      await connectDatabase();
    } catch (error) {
      console.warn('数据库连接失败，但服务器将继续运行');
    }

    // 设置优雅关闭
    setupGracefulShutdown();

    // 创建应用
    const app = createApp();
    const config = getConfig();

    // 启动服务器
    const server = app.listen(config.server.port, config.server.host, () => {
      printConfig();
      console.log(`🚀 服务器运行在 http://${config.server.host}:${config.server.port}`);
      console.log(`📋 API 文档: http://${config.server.host}:${config.server.port}/api-docs`);
      console.log(`💚 健康检查: http://${config.server.host}:${config.server.port}/health`);
      console.log('');
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n🛑 收到 ${signal} 信号，正在关闭服务器...`);
      
      server.close((err) => {
        if (err) {
          console.error('❌ 关闭服务器失败:', err);
          process.exit(1);
        }
        
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        console.error('❌ 强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return { app, server };
  } catch (error) {
    console.error('❌ 启动应用失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动应用
if (require.main === module) {
  startApp();
}

module.exports = { createApp, startApp };
