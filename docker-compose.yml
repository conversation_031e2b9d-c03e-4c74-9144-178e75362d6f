version: "3.9"

services:
  mongo:
    image: mongo:6.0
    container_name: aitools-mongo
    restart: unless-stopped
    volumes:
      - mongo_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: laixiao
      MONGO_INITDB_ROOT_PASSWORD: laixiao4720425
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: aitools-server
    restart: unless-stopped
    env_file:
      - ./server/.env

    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      # 使用 compose 内的 mongo 服务
      MONGODB_URI: *************************************************************
      # 生产模式下必需
      JWT_SECRET: prd-U6k!9sQ3@eW8rT2%yI5*oP7dA1$vB4^mC0zXlD
    ports:
      - "3003:3000"
    volumes:
      - ./server/uploads:/app/uploads
      - ./server/public:/app/public
    depends_on:
      mongo:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mongo_data:

