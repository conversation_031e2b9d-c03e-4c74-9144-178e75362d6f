# syntax=docker/dockerfile:1

# --- Install production dependencies ---
FROM node:18-bookworm-slim AS deps
WORKDIR /app
# Install OS packages needed for healthcheck (curl)
RUN apt-get update && apt-get install -y --no-install-recommends curl \
    && rm -rf /var/lib/apt/lists/*

# Copy lockfiles first for better caching
COPY package*.json ./
# Install only production deps
RUN npm ci --only=production

# --- Runtime image ---
FROM node:18-bookworm-slim AS runner
WORKDIR /app
ENV NODE_ENV=production
ENV HOST=0.0.0.0

# Install runtime tools for healthcheck
RUN apt-get update && apt-get install -y --no-install-recommends curl \
    && rm -rf /var/lib/apt/lists/*

# Copy node_modules from deps stage
COPY --from=deps /app/node_modules ./node_modules
# Copy application source
COPY . .

# Ensure uploads directory exists and is writable (will be mounted as a volume in compose)
RUN mkdir -p uploads && chown -R node:node uploads

EXPOSE 3000
USER node

CMD ["node", "src/app.js"]

