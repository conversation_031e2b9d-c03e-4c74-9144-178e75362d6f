#!/usr/bin/env bash
# 一键部署/更新/运行脚本（Linux/macOS）
# 用法：
#   ./deploy.sh             # 等同于 up
#   ./deploy.sh up          # 构建并启动
#   ./deploy.sh pull        # 拉取镜像并重启
#   ./deploy.sh rebuild     # 重新构建后端镜像并重启
#   ./deploy.sh restart     # 重启容器
#   ./deploy.sh down        # 停止并移除
#   ./deploy.sh logs        # 查看后端日志
#   ./deploy.sh status      # 查看当前状态

set -euo pipefail

command -v docker >/dev/null 2>&1 || { echo 'Docker 未安装或不可用'; exit 1; }

compose() {
  if docker compose version >/dev/null 2>&1; then
    docker compose "$@"
  elif command -v docker-compose >/dev/null 2>&1; then
    docker-compose "$@"
  else
    echo '未检测到 Docker Compose，请安装 Docker Desktop（或 docker-compose）。'
    exit 1
  fi
}

ACTION=${1:-up}

echo "==> 使用 docker-compose.yml 执行: $ACTION"
case "$ACTION" in
  up)
    compose pull
    compose build server
    compose up -d
    compose ps
    ;;
  pull)
    compose pull
    compose up -d
    compose ps
    ;;
  rebuild)
    compose build --no-cache server
    compose up -d server
    compose ps
    ;;
  restart)
    if ! compose restart; then compose up -d; fi
    compose ps
    ;;
  down)
    compose down
    ;;
  logs)
    echo '按 Ctrl+C 退出日志跟随'
    compose logs -f server
    ;;
  status)
    compose ps
    ;;
  *)
    echo "未知操作: $ACTION"
    echo "用法: $0 [up|pull|rebuild|restart|down|logs|status]"
    exit 1
    ;;

esac

