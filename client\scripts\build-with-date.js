#!/usr/bin/env node

/**
 * 构建脚本 - 设置构建日期并执行 electron-builder
 */

const { spawn } = require('child_process');
const path = require('path');

// 获取当前日期 (YYYY-MM-DD 格式)
const buildDate = new Date().toISOString().split('T')[0];

// 获取命令行参数
const args = process.argv.slice(2);

// 设置环境变量
const env = {
  ...process.env,
  BUILD_DATE: buildDate
};

console.log(`🗓️  设置构建日期: ${buildDate}`);
console.log(`🔨 开始构建...`);

// 执行 electron-builder
const electronBuilder = spawn('electron-builder', args, {
  env,
  stdio: 'inherit',
  shell: true
});

electronBuilder.on('close', (code) => {
  if (code === 0) {
    console.log(`✅ 构建完成! 构建日期: ${buildDate}`);
  } else {
    console.error(`❌ 构建失败，退出码: ${code}`);
    process.exit(code);
  }
});

electronBuilder.on('error', (error) => {
  console.error('❌ 构建过程中发生错误:', error);
  process.exit(1);
});
